/**
 * SimpleZombieAnimationHandler.js
 *
 * A simplified animation handler for zombie enemies based on the skeleton animation approach.
 * This handler uses simple, direct rotations rather than complex calculations.
 */

import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

export class SimpleZombieAnimationHandler {
    /**
     * Create a zombie animation handler
     * @param {THREE.Group} zombieModel - The zombie model to animate
     */
    constructor(zombieModel) {
        this.zombieModel = zombieModel;

        // Get body parts - search directly in the model
        this.bodyGroup = zombieModel.getObjectByName('body') || zombieModel.getObjectByName('core');
        this.headGroup = zombieModel.getObjectByName('head');
        this.leftArmGroup = zombieModel.getObjectByName('leftArm');
        this.rightArmGroup = zombieModel.getObjectByName('rightArm');
        this.leftLegGroup = zombieModel.getObjectByName('leftLeg');
        this.rightLegGroup = zombieModel.getObjectByName('rightLeg');

        // Debug output to check if all parts are found
        console.log('SimpleZombieAnimationHandler initialized with parts:', {
            body: !!this.bodyGroup,
            head: !!this.headGroup,
            leftArm: !!this.leftArmGroup,
            rightArm: !!this.rightArmGroup,
            leftLeg: !!this.leftLegGroup,
            rightLeg: !!this.rightLegGroup
        });

        // Get animation data from model or use defaults
        this.animationData = zombieModel.userData.animationData || {
            walkSpeed: 1.5,              // Walk speed multiplier
            walkAmplitude: Math.PI / 6,  // Amplitude for leg movement
            armSwingAmplitude: Math.PI / 4, // Amplitude for arm swing
            attackDuration: 1.0,         // Duration of attack animation in seconds
            idleSpeed: 1.2               // Speed of idle animation
        };

        // Animation state
        this.currentState = AIStates.IDLE;
        this.attackProgress = 0;
        this.lastHitTime = 0;
        this.animationTime = 0;

        // Store original positions and rotations
        this.storeOriginalTransforms();

        // Store base position for reference (but don't modify it directly)
        if (zombieModel) {
            zombieModel.userData.basePositionY = zombieModel.position.y;
            zombieModel.userData.basePositionZ = zombieModel.position.z;
        }
    }

    /**
     * Store original positions and rotations of all body parts
     */
    storeOriginalTransforms() {
        this.originalPositions = {};
        this.originalRotations = {};

        const groups = [
            'zombieModel', 'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                this.originalPositions[groupName] = group.position.clone();
                this.originalRotations[groupName] = group.rotation.clone();
            }
        }
    }

    /**
     * Reset all body parts to original positions
     */
    resetTransforms() {
        const groups = [
            'zombieModel', 'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                if (this.originalPositions[groupName]) {
                    group.position.copy(this.originalPositions[groupName]);
                }
                if (this.originalRotations[groupName]) {
                    group.rotation.copy(this.originalRotations[groupName]);
                }
            }
        }
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.zombieModel) return;

        // Store current position before animation update
        const currentPosition = this.zombieModel.position.clone();

        // Update animation time
        this.animationTime += deltaTime;

        // Handle state transition
        if (this.currentState !== state) {
            this.currentState = state;

            // Reset attack progress when entering attack state
            if (state === AIStates.ATTACKING) {
                this.attackProgress = 0;
            }

            // Set lastHitTime when entering hit reaction state
            if (state === AIStates.HIT_REACTING) {
                // Always reset the hit animation completely
                this.lastHitTime = globalTime;

                // Use the lastHitTime from the enemy's userData if available
                if (this.zombieModel && this.zombieModel.userData && this.zombieModel.userData.lastHitTime) {
                    this.lastHitTime = this.zombieModel.userData.lastHitTime;
                }

                console.log(`[SimpleZombieAnimationHandler] Hit reaction at time ${this.lastHitTime}`);
            }
        }

        // Update attack progress
        if (state === AIStates.ATTACKING) {
            this.attackProgress += deltaTime / this.animationData.attackDuration;
            if (this.attackProgress >= 1.0) {
                this.attackProgress = 0;
            }
        }

        // Apply animations based on state
        switch (state) {
            case AIStates.IDLE:
                this.applyIdleAnimation(globalTime);
                break;

            case AIStates.MOVING:
            case AIStates.STRAFING:
            case AIStates.FLEEING:
                this.applyWalkingAnimation(globalTime);
                break;

            case AIStates.ATTACKING:
                this.applyAttackingAnimation(globalTime);
                break;

            case AIStates.HIT_REACTING:
                this.applyHitReactionAnimation(globalTime);
                break;

            default:
                this.applyIdleAnimation(globalTime);
                break;
        }

        // IMPORTANT: Restore position if AI brain has set a position
        // This ensures that animation doesn't override the AI movement
        // BUT NOT during knockback - let knockback physics handle position
        if (this.zombieModel.userData.lastAIPosition &&
            (!this.zombieModel.userData.aiBrain || !this.zombieModel.userData.aiBrain.isKnockedBack)) {
            // Only restore X and Z, leave Y alone to avoid floor collision issues
            this.zombieModel.position.x = this.zombieModel.userData.lastAIPosition.x;
            this.zombieModel.position.z = this.zombieModel.userData.lastAIPosition.z;

            // CRITICAL: Force the zombie to face the direction it's moving
            // This is the most direct way to ensure proper rotation
            if (state === AIStates.MOVING || state === AIStates.STRAFING || state === AIStates.FLEEING) {
                // If we have a stored movement direction, use it to set rotation
                if (this.zombieModel.userData.lastMoveDirection) {
                    const moveDir = this.zombieModel.userData.lastMoveDirection;
                    if (moveDir.lengthSq() > 0.001) {
                        // Create a normalized direction vector in the XZ plane
                        const flatDir = new THREE.Vector3(moveDir.x, 0, moveDir.z).normalize();
                        // Calculate the target position to look at
                        const targetPos = this.zombieModel.position.clone().add(flatDir);
                        // Make the zombie look at the target position
                        this.zombieModel.lookAt(targetPos);
                    }
                }
            }
        } else {
            // If no AI position is set, restore the original position
            this.zombieModel.position.copy(currentPosition);
        }
    }

    /**
     * Apply idle animation
     * @param {number} time - Global time
     */
    applyIdleAnimation(time) {
        // IMPORTANT: Save the current rotation before resetting transforms
        const currentRotation = this.zombieModel ? this.zombieModel.rotation.clone() : null;

        // Reset transforms first
        this.resetTransforms();

        // IMPORTANT: Restore the rotation after resetting transforms
        if (currentRotation && this.zombieModel) {
            this.zombieModel.rotation.copy(currentRotation);
        }

        const idleSpeed = this.animationData.idleSpeed;
        const idleBobAmplitude = 0.05;
        const idleSwayAmplitude = Math.PI / 32;

        // Subtle idle movement
        const bobOffset = Math.sin(time * idleSpeed) * idleBobAmplitude;
        const swayOffset = Math.sin(time * idleSpeed * 0.7) * idleSwayAmplitude;

        // Apply to limbs
        if (this.leftLegGroup) this.leftLegGroup.rotation.x = swayOffset;
        if (this.rightLegGroup) this.rightLegGroup.rotation.x = -swayOffset;

        // Arms hang down with slight movement
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = swayOffset * 1.5;
            this.leftArmGroup.rotation.z = 0.2; // Slightly out from body
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = -swayOffset * 1.5;
            this.rightArmGroup.rotation.z = -0.2; // Slightly out from body
        }

        // Head movement
        if (this.headGroup) {
            this.headGroup.rotation.x = Math.sin(time * idleSpeed * 0.8) * (idleSwayAmplitude * 0.5);
            this.headGroup.rotation.z = Math.sin(time * idleSpeed * 1.1) * (idleSwayAmplitude * 0.3);
        }

        // Body sway
        if (this.bodyGroup) {
            this.bodyGroup.rotation.z = Math.sin(time * idleSpeed * 0.5) * (idleSwayAmplitude * 0.5);
        }

        // IMPORTANT: Don't modify the zombie's position during idle animation
        // This interferes with the actual movement logic in the AI brain
        // Instead, we'll only animate the limbs and body rotations
    }

    /**
     * Apply walking animation
     * @param {number} time - Global time
     */
    applyWalkingAnimation(time) {
        // IMPORTANT: Save the current rotation before resetting transforms
        const currentRotation = this.zombieModel ? this.zombieModel.rotation.clone() : null;

        // Reset transforms first
        this.resetTransforms();

        // IMPORTANT: Restore the rotation after resetting transforms
        if (currentRotation && this.zombieModel) {
            this.zombieModel.rotation.copy(currentRotation);
        }

        const walkSpeed = this.animationData.walkSpeed * 2.0;
        const walkAmplitude = this.animationData.walkAmplitude;

        // Walk cycle for legs - reduced amplitude
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.7);
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.7);
        }

        // Arm swing - opposite to legs for natural counterbalance - reduced amplitude
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.5);
            this.leftArmGroup.rotation.z = 0.2 + Math.sin(time * walkSpeed * 0.5) * 0.05; // Slightly out from body
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.5);
            this.rightArmGroup.rotation.z = -0.2 - Math.sin(time * walkSpeed * 0.5) * 0.05; // Slightly out from body
        }

        // Body movement - much more subtle
        if (this.bodyGroup) {
            // Side-to-side sway - reduced by 75%
            this.bodyGroup.rotation.z = Math.sin(time * walkSpeed * 0.5) * (walkAmplitude * 0.05);
            // Forward lean - slight and constant
            this.bodyGroup.rotation.x = 0.05;
        }

        // Head movement
        if (this.headGroup) {
            // Head follows body with slight delay
            this.headGroup.rotation.z = Math.sin(time * walkSpeed * 0.5 - 0.2) * (walkAmplitude * 0.3);

            // IMPORTANT: Don't modify the head's position directly as it can cause the head to go into the ground
            // Instead, add a slight rotation to simulate the head bobbing
            this.headGroup.rotation.x = Math.sin(time * walkSpeed) * 0.1;
        }

        // IMPORTANT: Don't modify the zombie's position during walking animation
        // This interferes with the actual movement logic in the AI brain
        // Instead, we'll only animate the limbs and body rotations
    }

    /**
     * Apply attacking animation
     * @param {number} time - Global time
     */
    applyAttackingAnimation(time) {
        // IMPORTANT: Save the current rotation before resetting transforms
        const currentRotation = this.zombieModel ? this.zombieModel.rotation.clone() : null;

        // Reset transforms first
        this.resetTransforms();

        // IMPORTANT: Restore the rotation after resetting transforms
        if (currentRotation && this.zombieModel) {
            this.zombieModel.rotation.copy(currentRotation);
        }

        // Use attack progress (0 to 1) to determine animation phase
        const progress = this.attackProgress;

        // Divide into phases: windup (0-0.3), strike (0.3-0.7), recover (0.7-1.0)
        const windupPhase = progress < 0.3;
        const strikePhase = progress >= 0.3 && progress < 0.7;
        const recoverPhase = progress >= 0.7;

        // Determine which arm is attacking (alternate based on time)
        const isLeftArmAttack = Math.floor(time) % 2 === 0;
        const attackingArm = isLeftArmAttack ? this.leftArmGroup : this.rightArmGroup;
        const supportingArm = isLeftArmAttack ? this.rightArmGroup : this.leftArmGroup;

        // Store base positions if not already set
        if (this.zombieModel) {
            if (this.zombieModel.userData.basePositionY === undefined) {
                this.zombieModel.userData.basePositionY = this.originalPositions.zombieModel ?
                    this.originalPositions.zombieModel.y : 0;
            }
            if (this.zombieModel.userData.basePositionZ === undefined) {
                this.zombieModel.userData.basePositionZ = this.originalPositions.zombieModel ?
                    this.originalPositions.zombieModel.z : 0;
            }
        }

        // Body movement
        if (this.bodyGroup) {
            if (windupPhase) {
                // Wind up: lean back
                this.bodyGroup.rotation.x = -0.2;
            } else if (strikePhase) {
                // Strike: lunge forward
                this.bodyGroup.rotation.x = 0.3;
            } else {
                // Recover: return to neutral
                this.bodyGroup.rotation.x = 0.1;
            }
        }

        // Arms
        if (attackingArm && supportingArm) {
            if (windupPhase) {
                // Wind up: raise attacking arm back
                attackingArm.rotation.x = -Math.PI/3;
                attackingArm.rotation.z = isLeftArmAttack ? 0.4 : -0.4;

                // Supporting arm up for balance
                supportingArm.rotation.x = Math.PI/6;
                supportingArm.rotation.z = isLeftArmAttack ? -0.2 : 0.2;
            } else if (strikePhase) {
                // Strike: swing attacking arm forward
                attackingArm.rotation.x = Math.PI/4;
                attackingArm.rotation.z = isLeftArmAttack ? 0.2 : -0.2;

                // Supporting arm follows through
                supportingArm.rotation.x = Math.PI/4;
                supportingArm.rotation.z = isLeftArmAttack ? -0.3 : 0.3;
            } else {
                // Recover: return to neutral
                attackingArm.rotation.x = 0;
                attackingArm.rotation.z = isLeftArmAttack ? 0.2 : -0.2;

                supportingArm.rotation.x = 0;
                supportingArm.rotation.z = isLeftArmAttack ? -0.2 : 0.2;
            }
        }

        // Head follows the attack motion
        if (this.headGroup) {
            if (windupPhase) {
                // Look up during windup
                this.headGroup.rotation.x = -0.2;
            } else if (strikePhase) {
                // Look down during strike
                this.headGroup.rotation.x = 0.3;
            } else {
                // Return to neutral
                this.headGroup.rotation.x = 0;
            }
        }

        // Legs provide stable base
        if (this.leftLegGroup && this.rightLegGroup) {
            // Wider stance during attack
            this.leftLegGroup.rotation.z = -0.2;
            this.rightLegGroup.rotation.z = 0.2;

            // Bend knees during strike
            if (strikePhase) {
                this.leftLegGroup.rotation.x = 0.2;
                this.rightLegGroup.rotation.x = 0.2;
            }
        }

        // IMPORTANT: Don't modify the zombie's position during attack animation
        // This interferes with the actual movement logic in the AI brain
        // Instead, we'll only animate the limbs and body rotations
        // We can simulate forward movement by rotating the body more aggressively
        if (this.bodyGroup) {
            if (windupPhase) {
                // Exaggerate the backward lean during windup
                const windupProgress = progress / 0.3; // 0 to 1 during windup phase
                this.bodyGroup.rotation.x = -0.3 * windupProgress;
            } else if (strikePhase) {
                // Exaggerate the forward lean during strike
                const strikeProgress = (progress - 0.3) / 0.4; // 0 to 1 during strike phase
                this.bodyGroup.rotation.x = -0.3 + (0.6 * strikeProgress);
            } else if (recoverPhase) {
                // Return to neutral during recovery
                const recoverProgress = (progress - 0.7) / 0.3; // 0 to 1 during recover phase
                this.bodyGroup.rotation.x = 0.3 - (0.3 * recoverProgress);
            }
        }
    }

    /**
     * Apply hit reaction animation
     * @param {number} time - Global time
     */
    applyHitReactionAnimation(time) {
        // EMERGENCY FIX: Preserve the current rotation quaternion
        const currentRotation = this.zombieModel ? this.zombieModel.quaternion.clone() : null;

        // Reset transforms first
        this.resetTransforms();

        // EMERGENCY FIX: Restore the exact rotation quaternion
        // This ensures perfect rotation preservation
        if (currentRotation && this.zombieModel) {
            this.zombieModel.quaternion.copy(currentRotation);
        }

        // Calculate time since hit
        const timeSinceHit = time - this.lastHitTime;
        const hitReactionDuration = 0.5; // 0.5 seconds
        const progress = Math.min(1.0, timeSinceHit / hitReactionDuration);

        // Stagger backwards
        if (this.bodyGroup) {
            // Lean back, gradually recover
            this.bodyGroup.rotation.x = -0.3 * (1 - progress);
            // Wobble
            this.bodyGroup.rotation.z = Math.sin(time * 15) * 0.1 * (1 - progress);
        }

        // Arms flail
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = Math.PI/6;
            this.leftArmGroup.rotation.z = 0.4 + Math.sin(time * 12) * 0.2 * (1 - progress);
        }

        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.PI/6;
            this.rightArmGroup.rotation.z = -0.4 - Math.sin(time * 12) * 0.2 * (1 - progress);
        }

        // Head jerks back
        if (this.headGroup) {
            this.headGroup.rotation.x = -0.3 * (1 - progress) + Math.sin(time * 15) * 0.1 * (1 - progress);
        }

        // Legs buckle
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = -0.1 * (1 - progress);
            this.leftLegGroup.rotation.z = 0.1;
        }

        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = -0.1 * (1 - progress);
            this.rightLegGroup.rotation.z = -0.1;
        }

        // IMPORTANT: Don't modify the zombie's position during hit reaction animation
        // This interferes with the actual movement logic in the AI brain
        // Instead, we'll only animate the limbs and body rotations
        // We can simulate backward movement by rotating the body more aggressively
        if (this.bodyGroup) {
            // Exaggerate the backward lean during hit reaction
            this.bodyGroup.rotation.x = -0.5 * (1 - progress);
            // Add more dramatic wobble
            this.bodyGroup.rotation.z = Math.sin(time * 20) * 0.2 * (1 - progress);
        }
    }
}
